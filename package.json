{"name": "gloopi-ecommerce", "version": "1.0.0", "private": true, "packageManager": "bun@1.2.19", "workspaces": ["apps/*", "packages/*"], "scripts": {"prepare": "husky install", "update": "npx npm-check-updates -u", "dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "type-check": "turbo run type-check", "db:generate": "turbo run db:generate", "db:format": "turbo run db:format", "db:reset": "turbo run db:reset", "db:push": "turbo run db:push", "db:seed": "turbo run db:seed"}, "author": {"name": "Gloopi Indonesia", "url": "https://github.com/gloopi-indonesia"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "turbo": "^2.5.5", "eslint": "^9.12.0", "eslint-config-next": "^14.2.15", "eslint-plugin-react": "^7.37.1", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8"}}