{"extends": "next", "rules": {"react/prop-types": 0, "react/no-unescaped-entities": 0, "@next/next/no-server-import-in-page": 0, "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-empty-object-type": "warn", "prefer-const": "warn", "no-unsafe-optional-chaining": "warn", "@typescript-eslint/no-unused-expressions": "warn", "@typescript-eslint/ban-ts-comment": "warn"}}